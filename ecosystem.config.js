module.exports = {
    apps: [
        // 后端API服务器
        {
            name: 'lost-pet-server',
            script: './server/src/app.js',
            cwd: './',
            instances: 1,
            exec_mode: 'fork',
            env: {
                NODE_ENV: 'production',
                PORT: 3000
            },
            env_production: {
                NODE_ENV: 'production',
                PORT: 3000
            },
            // 日志配置
            log_file: './logs/server-combined.log',
            out_file: './logs/server-out.log',
            error_file: './logs/server-error.log',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

            // 自动重启配置
            watch: false,
            ignore_watch: ['node_modules', 'logs', 'uploads'],
            max_memory_restart: '1G',

            // 进程管理
            min_uptime: '10s',
            max_restarts: 10,
            autorestart: true,

            // 其他配置
            merge_logs: true,
            time: true
        },

        // 用户端前端服务
        {
            name: 'lost-pet-client',
            script: 'bun',
            args: 'run dev --host',
            cwd: './client',
            instances: 1,
            exec_mode: 'fork',
            env: {
                NODE_ENV: 'production',
                HOST: '0.0.0.0'
            },
            // 日志配置
            log_file: '../logs/client-combined.log',
            out_file: '../logs/client-out.log',
            error_file: '../logs/client-error.log',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

            // 自动重启配置
            watch: false,
            max_memory_restart: '500M',

            // 进程管理
            min_uptime: '10s',
            max_restarts: 10,
            autorestart: true,

            // 其他配置
            merge_logs: true,
            time: true
        },

        // 管理员端前端服务
        {
            name: 'lost-pet-admin',
            script: 'bun',
            args: 'run dev --host',
            cwd: './admin',
            instances: 1,
            exec_mode: 'fork',
            env: {
                NODE_ENV: 'production',
                HOST: '0.0.0.0'
            },
            // 日志配置
            log_file: '../logs/admin-combined.log',
            out_file: '../logs/admin-out.log',
            error_file: '../logs/admin-error.log',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

            // 自动重启配置
            watch: false,
            max_memory_restart: '500M',

            // 进程管理
            min_uptime: '10s',
            max_restarts: 10,
            autorestart: true,

            // 其他配置
            merge_logs: true,
            time: true
        }
    ]
};